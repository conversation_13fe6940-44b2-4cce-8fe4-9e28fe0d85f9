# Dataflow Kit 点选式前端设计方案

## 1. 项目目标

开发一个基于 Web 的前端界面，允许用户通过点选网页元素的方式，自动生成用于 Dataflow Kit 的 JSON 配置文件（Payload）。该前端需要具备以下核心功能：

1.  在浏览器中加载目标网页。
2.  允许用户点击页面元素以选择数据。
3.  自动生成对应的 CSS 选择器。
4.  支持用户编辑选择器、字段名、属性等。
5.  支持添加分页规则。
6.  实时预览生成的 JSON Payload。
7.  提供将 Payload 发送到 DFK Parse 服务的选项。

## 2. 整体架构

```
+------------------+       +------------------+       +------------------+
|                  |       |                  |       |                  |
|   用户浏览器      |<----->|   Web 前端应用    |<----->|  DFK Parse 服务   |
| (目标网站)        |       | (React/Vue.js)   |       | (后端)           |
+------------------+       +------------------+       +------------------+
                                |       ^
                                |       |
                                v       |
                        +------------------+
                        |                  |
                        |   Chrome 扩展    | (可选，用于增强功能)
                        |                  |
                        +------------------+
```

### 2.1 核心组件

1.  **Web 前端应用**：
    *   **技术栈**：React (或 Vue.js) + Bootstrap CSS + Material Design。
    *   **功能**：
        *   URL 输入框：用户输入要分析的目标网页 URL。
        *   内嵌 iframe 或新窗口：加载目标网页。
        *   点选模式：激活后，用户点击 iframe 中的元素，前端捕获点击事件。
        *   元素高亮：在用户悬停或点击时，高亮显示选中的元素。
        *   选择器生成与编辑：自动尝试生成 CSS 选择器，并允许用户手动调整。
        *   字段配置面板：为每个选中的元素配置字段名、要提取的属性（text, href, src 等）、过滤器。
        *   分页配置：允许用户指定分页的 CSS 选择器。
        *   JSON Payload 预览：实时显示根据用户配置生成的 JSON。
        *   发送按钮：将生成的 JSON Payload 通过 HTTP POST 请求发送到 DFK Parse 服务。
2.  **DFK Parse 服务**：
    *   **功能**：接收前端发送的 JSON Payload，执行抓取和解析任务，并返回结果。
    *   **交互**：前端通过 HTTP API 与之通信。
3.  **Chrome 扩展 (可选)**：
    *   **功能**：如果需要更强大的选择器生成能力或与浏览器更深度的集成，可以开发一个 Chrome 扩展。它可以直接在目标网页上下文中运行，获取更准确的元素信息。

## 3. 详细设计

### 3.1 前端应用设计

#### 3.1.1 技术选型

*   **框架**：React (使用 Create React App 初始化项目)
*   **UI 库**：Material-UI 或 Ant Design，用于构建美观的界面组件。
*   **状态管理**：Redux 或 Context API，管理应用状态（如选中的字段、生成的 Payload 等）。
*   **HTTP 客户端**：Axios，用于与后端 DFK Parse 服务通信。
*   **选择器生成**：利用浏览器原生的 `document.querySelector` 和 `element.matches` API 来辅助生成和验证选择器。可以集成 `css-selector-generator` 这样的库来简化复杂选择器的生成。
*   **代码编辑器**：使用 Monaco Editor (VS Code 的编辑器) 或 CodeMirror 来显示和编辑 JSON Payload。

#### 3.1.2 核心功能实现

1.  **加载目标网页**：
    *   用户在输入框中输入 URL。
    *   前端应用将该 URL 加载到一个 `<iframe>` 中。
    *   注意：需要处理跨域问题。如果目标网站设置了 `X-Frame-Options` 或 `Content-Security-Policy` 禁止被嵌入 iframe，则可能需要提示用户或提供替代方案（如在新标签页中打开，并通过扩展通信）。

2.  **点选模式与元素选择**：
    *   提供一个“启用点选”按钮。
    *   激活后，在 iframe 上覆盖一层透明的 div，捕获点击事件。
    *   当用户点击时，通过 `postMessage` API 与 iframe 内的页面通信，获取被点击元素的引用或基本信息（如标签名、类名、ID 等）。
    *   如果使用 Chrome 扩展，可以直接在 iframe 内注入脚本，更方便地获取元素信息。
    *   根据获取的元素信息，调用选择器生成库生成初步的 CSS 选择器。

3.  **选择器生成与验证**：
    *   使用如 `css-selector-generator` 库，根据元素的唯一性生成选择器（优先使用 ID，然后是类名、标签名、属性等组合）。
    *   提供一个“验证”按钮，检查生成的选择器是否能在当前页面唯一匹配到目标元素。
    *   如果不唯一或无法匹配，提示用户手动调整。

4.  **字段配置**：
    *   每当用户选择一个元素，就在侧边栏或下方区域添加一个字段配置项。
    *   配置项包括：
        *   字段名 (Name)：默认可以是元素的文本内容或标签名。
        *   CSS 选择器 (Selector)：自动生成，可编辑。
        *   提取属性 (Attributes)：多选框，如 `text`, `href`, `src`, `alt`, `innerHTML` 等。
        *   过滤器 (Filters)：下拉菜单或多选，如 `trim`, `lowerCase`, `upperCase`, `regex` 等。
        *   删除按钮：移除该字段。

5.  **分页配置**：
    *   提供一个专门的区域用于配置分页。
    *   用户可以通过点选“下一页”链接来设置分页选择器。
    *   允许设置最大页数。

6.  **JSON Payload 生成与预览**：
    *   根据用户在 UI 上的所有配置（字段、分页、格式等），实时构建 JSON 对象。
    *   使用 Monaco Editor 组件显示生成的 JSON，支持语法高亮和格式化。
    *   允许用户直接在编辑器中修改 JSON，修改后应能反向同步到 UI 配置（可选，较复杂）。

7.  **发送到 DFK**：
    *   提供一个“发送到 DFK”按钮。
    *   用户输入 DFK Parse 服务的地址（如 `http://localhost:8001`）。
    *   点击按钮后，使用 Axios 发送 POST 请求，将 JSON Payload 发送到 `/parse` 端点。
    *   显示 DFK 返回的结果（可以是原始数据或下载链接）。

### 3.2 与 DFK 后端的交互

*   **发送 Payload**：
    ```javascript
    const payload = { /* ... 构建好的 JSON 对象 ... */ };
    const dfkParseUrl = 'http://localhost:8001/parse'; // 用户可配置

    axios.post(dfkParseUrl, payload, {
      headers: {
        'Content-Type': 'application/json'
      },
      responseType: 'blob' // 如果期望返回文件 (CSV, Excel)
    })
    .then(response => {
      // 处理返回的结果
      // 如果是 blob，可以创建下载链接
      // 如果是 JSON，直接显示
    })
    .catch(error => {
      console.error('发送到 DFK 失败:', error);
      // 在 UI 上显示错误信息
    });
    ```

### 3.3 Chrome 扩展 (可选)

如果需要更强大的功能，可以开发 Chrome 扩展：

1.  **Content Script**：注入到目标网页，负责监听用户点击、生成选择器、高亮元素。
2.  **Background Script**：作为中间层，处理 Content Script 和 Popup Page 之间的通信。
3.  **Popup Page**：扩展的弹出界面，可以是一个简化版的前端应用，或者只提供配置和发送功能。

## 4. 开发计划

1.  **第一阶段：基础框架搭建**
    *   使用 Create React App 初始化项目。
    *   集成 UI 库和状态管理。
    *   创建基本的页面布局（URL 输入、iframe、配置面板、JSON 预览）。

2.  **第二阶段：核心功能开发**
    *   实现目标网页加载到 iframe。
    *   开发点选模式和元素选择逻辑。
    *   集成选择器生成库。
    *   实现字段配置面板的基本功能（添加、编辑字段名、选择器）。

3.  **第三阶段：高级功能**
    *   实现分页配置。
    *   实现属性和过滤器的选择。
    *   集成 Monaco Editor 进行 JSON 预览和编辑。

4.  **第四阶段：与后端集成**
    *   实现发送到 DFK Parse 服务的功能。
    *   处理并展示 DFK 返回的结果。

5.  **第五阶段：优化与测试**
    *   优化 UI/UX，确保在不同网站上的兼容性。
    *   进行充分的测试，包括各种网页结构和选择器复杂度。
    *   编写文档和使用说明。

## 5. 关键技术点

*   **跨域通信 (postMessage)**：这是 iframe 与主应用通信的关键技术。
*   **CSS 选择器生成**：需要一个可靠的库或算法来生成稳定且唯一的选择器。
*   **浏览器 API (querySelector, matches)**：用于验证选择器。
*   **React 状态管理**：高效地管理复杂的 UI 状态。
*   **HTTP 客户端 (Axios)**：与后端 API 交互。

## 6. 预期成果

一个功能完善、用户友好的 Web 前端应用，能够显著降低 Dataflow Kit 的使用门槛，让非技术人员也能通过简单的点选操作完成网页数据的提取配置。