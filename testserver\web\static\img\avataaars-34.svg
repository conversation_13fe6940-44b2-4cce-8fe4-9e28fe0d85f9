<svg width="264px" height="280px" viewBox="0 0 264 280" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><desc>Created with getavataaars.com</desc><defs><circle id="path-1" cx="120" cy="120" r="120"></circle><path d="M12,160 C12,226.27417 65.72583,280 132,280 C198.27417,280 252,226.27417 252,160 L264,160 L264,-1.42108547e-14 L-3.19744231e-14,-1.42108547e-14 L-3.19744231e-14,160 L12,160 Z" id="path-3"></path><path d="M124,144.610951 L124,163 L128,163 L128,163 C167.764502,163 200,195.235498 200,235 L200,244 L0,244 L0,235 C-4.86974701e-15,195.235498 32.235498,163 72,163 L72,163 L76,163 L76,144.610951 C58.7626345,136.422372 46.3722246,119.687011 44.3051388,99.8812385 C38.4803105,99.0577866 34,94.0521096 34,88 L34,74 C34,68.0540074 38.3245733,63.1180731 44,62.1659169 L44,56 L44,56 C44,25.072054 69.072054,5.68137151e-15 100,0 L100,0 L100,0 C130.927946,-5.68137151e-15 156,25.072054 156,56 L156,62.1659169 C161.675427,63.1180731 166,68.0540074 166,74 L166,88 C166,94.0521096 161.51969,99.0577866 155.694861,99.8812385 C153.627775,119.687011 141.237365,136.422372 124,144.610951 Z" id="path-5"></path></defs><g id="Avataaar" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd"><g transform="translate(-825.000000, -1100.000000)" id="Avataaar/Circle"><g transform="translate(825.000000, 1100.000000)"><g id="Circle" stroke-width="1" fill-rule="evenodd" transform="translate(12.000000, 40.000000)"><mask id="mask-2" fill="white"><use xlink:href="#path-1"></use></mask><use id="Circle-Background" fill="#E6E6E6" xlink:href="#path-1"></use><g id="Color/Palette/Blue-01" mask="url(#mask-2)" fill="#65C9FF"><rect id="🖍Color" x="0" y="0" width="240" height="240"></rect></g></g><mask id="mask-4" fill="white"><use xlink:href="#path-3"></use></mask><g id="Mask"></g><g id="Avataaar" stroke-width="1" fill-rule="evenodd" mask="url(#mask-4)"><g id="Body" transform="translate(32.000000, 36.000000)"><mask id="mask-6" fill="white"><use xlink:href="#path-5"></use></mask><use fill="#D0C6AC" xlink:href="#path-5"></use><g id="Skin/👶🏽-03-Brown" mask="url(#mask-6)" fill="#F8D25C"><g transform="translate(-32.000000, 0.000000)" id="Color"><rect x="0" y="0" width="264" height="244"></rect></g></g><path d="M156,79 L156,102 C156,132.927946 130.927946,158 100,158 C69.072054,158 44,132.927946 44,102 L44,79 L44,94 C44,124.927946 69.072054,150 100,150 C130.927946,150 156,124.927946 156,94 L156,79 Z" id="Neck-Shadow" fill-opacity="0.100000001" fill="#000000" mask="url(#mask-6)"></path></g><g id="Clothing/Graphic-Shirt" transform="translate(0.000000, 170.000000)"><defs><path d="M165.624032,29.2681342 C202.760022,32.1373245 232,63.1798426 232,101.051724 L232,110 L32,110 L32,101.051724 C32,62.8348009 61.7752018,31.5722494 99.3929298,29.1967444 C99.1342224,30.2735458 99,31.3767131 99,32.5 C99,44.3741221 113.998461,54 132.5,54 C151.001539,54 166,44.3741221 166,32.5 C166,31.4015235 165.871641,30.3222877 165.624025,29.2681336 Z" id="react-path-487"></path></defs><mask id="react-mask-488" fill="white"><use xlink:href="#react-path-487"></use></mask><use id="Clothes" fill="#E6E6E6" fill-rule="evenodd" xlink:href="#react-path-487"></use><g id="Color/Palette/Gray-01" mask="url(#react-mask-488)" fill-rule="evenodd" fill="#FFDEB5"><rect id="🖍Color" x="0" y="0" width="264" height="110"></rect></g><g id="Clothing/Graphic/Hola" mask="url(#react-mask-488)" fill-rule="evenodd" fill="#FFFFFF"><g transform="translate(77.000000, 58.000000)" id="Hola"><path d="M74.6371722,16.9479681 C75.917206,22.7098952 73.7328313,28.1917851 69.3072818,31.9519351 C67.2267705,33.7200766 64.8728066,35.0262382 62.2514759,35.7865716 C60.7921156,36.2102906 59.2674351,36.5090512 57.7541146,36.6572104 C56.61811,36.7683298 54.9700918,36.2570992 53.9631048,36.5456839 C52.8441403,36.8672382 51.4050658,38.4111058 50.2065809,38.9084974 C49.535121,39.1873134 48.8385068,39.4319389 48.1390525,39.6350472 C46.6326292,40.0734193 45.0742742,40.3689236 43.5256565,40.611514 C41.9242956,40.8622449 35.2433712,41.9140123 36.070626,38.9752504 C36.3550329,37.9641861 39.1816559,36.4707903 39.7950984,35.791049 C40.2486888,35.288773 42.9398027,32.427347 42.8140307,31.4960606 C42.7742705,31.2021844 41.0666121,29.8650885 40.6657648,29.4157265 C39.7573668,28.3973356 38.9041462,27.3581862 38.4205328,26.0646425 C37.5920608,23.8487672 37.6354724,21.0667121 37.9105478,18.7446017 C38.5986421,12.9407505 41.476791,9.08616941 46.5133487,6.19584415 C49.3679661,4.55795247 52.4720988,3.65678642 55.718232,3.26074551 C59.6739625,2.77841409 62.8279982,2.76294692 66.1848919,5.01097768 C70.6051671,7.97090519 73.4601902,11.6785482 74.6371722,16.9479681 Z M69.5056769,23.9358717 C70.5917785,23.6358901 69.1977385,20.8827342 68.427284,22.2161669 C68.1497742,22.6968702 68.8902058,24.1047895 69.5056769,23.9358717 Z M67.6848238,18.6143519 C67.7728641,18.973353 68.1185341,19.0836583 68.4325583,19.0405131 C69.182727,18.9371273 68.7125023,18.059976 68.7340052,17.6684125 C68.7741711,16.9194759 68.8407085,15.0292252 68.5619816,14.3474487 C68.0491567,13.0942011 67.5866405,13.3339422 67.2884393,14.5615469 C67.0210725,15.6617509 67.4109655,17.5092635 67.6848238,18.6143519 Z M66.6372652,22.892652 C67.4795315,22.3952604 65.2570196,16.7171816 64.7685376,15.9051553 C62.8770898,12.7604359 62.2652702,18.3363499 61.9751833,19.714149 C61.8498171,20.310856 60.4574,26.4960951 62.3135504,24.5020515 C63.3047144,23.4376661 61.755691,19.900162 64.5567538,20.7227711 C65.540615,21.0117629 64.8289892,20.9287287 65.4314774,21.8315228 C65.6517812,22.1616248 65.8887193,23.3326522 66.6372652,22.892652 Z M60.2947079,21.5987013 C61.0809854,20.2481733 57.6303713,20.8778498 57.2327696,20.9022717 C57.3268957,19.1027888 57.6782457,17.2792911 57.6733771,15.4798082 C57.67216,15.0052104 57.8380978,13.2887618 56.7146704,14.0987529 C56.2606742,14.4264127 56.4168749,15.9283561 56.3787376,16.3789391 C56.2886686,17.436405 55.4313908,21.0858425 56.2708171,21.9381649 C56.8660024,22.5421985 59.9640494,22.1587756 60.2947079,21.5987013 Z M55.4961584,19.1587406 C55.8337431,17.8148148 55.4118876,14.9057989 53.6512301,14.8794474 C53.0382604,14.8705068 53.3156518,15.7104604 53.164165,15.7984555 C52.0992431,16.4200683 51.6357538,16.3414844 51.0047261,17.4454234 C48.5177346,21.7957715 54.0705776,24.8897184 55.4961584,19.1587406 Z M49.4685836,22.9899323 C50.2301124,22.7799045 50.0146773,13.0115738 49.1375194,13.0062824 C47.6286618,12.9977348 49.0750392,17.8462849 48.5033854,18.49672 C48.3321733,18.6916877 47.4769241,18.5109661 47.2529689,18.5520762 C46.1129071,18.7637322 45.3623326,18.6444722 45.0126054,17.9464145 C44.7014213,17.3265067 45.3736926,13.0661159 43.7552917,15.1562188 C43.7698975,15.8937585 43.7816632,16.6317052 43.7901833,17.3692449 C44.1261161,18.0843979 44.0007498,18.5195137 43.4136789,18.6754065 C43.3844673,19.14512 43.8056005,19.5387187 43.8108748,19.6921693 C43.8360292,20.4630855 43.3268557,23.9232538 44.7111585,23.7563712 C45.5960249,23.6493221 45.0316741,20.2746303 45.0280226,19.7255458 C46.1705187,19.7532239 47.3673807,19.8806245 48.5033854,19.7336864 C48.5086597,20.1610687 48.2149214,23.3367225 49.4685836,22.9899323 Z M63.8368644,17 C64.2706568,17.6069046 64.6557203,18.2184508 65,18.8421816 C64.3342161,18.8990426 63.6679025,18.9518422 63,19 C63.2722458,18.332463 63.5439619,17.664926 63.8368644,17 Z M53.9230169,17 C54.0684325,17.4229772 54.1789882,20.2877939 52.5679624,19.9763934 C51.6476608,19.7985469 52.0614978,18.2121289 52.3219511,17.8848372 C52.8722396,17.1947521 53.1949428,17.4368397 53.9230169,17 Z"></path></g></g></g><g id="Face" transform="translate(76.000000, 82.000000)" fill="#000000"><g id="Mouth/Default" transform="translate(2.000000, 52.000000)" fill-opacity="0.699999988"><path d="M40,15 C40,22.7319865 46.2680135,29 54,29 L54,29 C61.7319865,29 68,22.7319865 68,15" id="Mouth"></path></g><g id="Nose/Default" transform="translate(28.000000, 40.000000)" fill-opacity="0.16"><path d="M16,8 C16,12.418278 21.372583,16 28,16 L28,16 C34.627417,16 40,12.418278 40,8" id="Nose"></path></g><g id="Eyes/Closed-😌" transform="translate(0.000000, 8.000000)" fill-opacity="0.599999964"><path d="M16.1601674,32.4473116 C18.006676,28.648508 22.1644225,26 26.9975803,26 C31.8136766,26 35.9591217,28.629842 37.8153518,32.4071242 C38.3667605,33.5291977 37.5821037,34.4474817 36.790607,33.7670228 C34.3395063,31.6597833 30.8587163,30.3437884 26.9975803,30.3437884 C23.2572061,30.3437884 19.8737584,31.5787519 17.4375392,33.5716412 C16.5467928,34.3002944 15.6201012,33.5583844 16.1601674,32.4473116 Z" id="Closed-Eye" transform="translate(27.000000, 30.000000) scale(1, -1) translate(-27.000000, -30.000000) "></path><path d="M74.1601674,32.4473116 C76.006676,28.648508 80.1644225,26 84.9975803,26 C89.8136766,26 93.9591217,28.629842 95.8153518,32.4071242 C96.3667605,33.5291977 95.5821037,34.4474817 94.790607,33.7670228 C92.3395063,31.6597833 88.8587163,30.3437884 84.9975803,30.3437884 C81.2572061,30.3437884 77.8737584,31.5787519 75.4375392,33.5716412 C74.5467928,34.3002944 73.6201012,33.5583844 74.1601674,32.4473116 Z" id="Closed-Eye" transform="translate(85.000000, 30.000000) scale(1, -1) translate(-85.000000, -30.000000) "></path></g><g id="Eyebrow/Natural/Default-Natural" fill-opacity="0.599999964"><path d="M26.0390934,6.21012364 C20.2775554,6.98346216 11.2929313,12.0052479 12.04426,17.8178111 C12.0689481,18.0080543 12.3567302,18.0673468 12.4809077,17.9084937 C14.9674041,14.7203351 34.1927973,10.0365481 41.1942673,11.0147151 C41.8350523,11.1044465 42.2580662,10.4430343 41.8210501,10.0302067 C38.0765663,6.49485426 31.2003792,5.51224825 26.0390934,6.21012364" id="Eyebrow" transform="translate(27.000000, 12.000000) rotate(5.000000) translate(-27.000000, -12.000000) "></path><path d="M85.0390934,6.21012364 C79.2775554,6.98346216 70.2929313,12.0052479 71.04426,17.8178111 C71.0689481,18.0080543 71.3567302,18.0673468 71.4809077,17.9084937 C73.9674041,14.7203351 93.1927973,10.0365481 100.194267,11.0147151 C100.835052,11.1044465 101.258066,10.4430343 100.82105,10.0302067 C97.0765663,6.49485426 90.2003792,5.51224825 85.0390934,6.21012364" id="Eyebrow" transform="translate(86.000000, 12.000000) scale(-1, 1) rotate(5.000000) translate(-86.000000, -12.000000) "></path></g></g><g id="Top" stroke-width="1" fill-rule="evenodd"><defs><rect id="react-path-498" x="0" y="0" width="264" height="280"></rect><path d="M167.308863,35.006118 C147.121091,23.3065109 127.128954,25.2219854 112.036894,29.0299707 C96.9448338,32.8379559 88.0168242,43.6513048 80.3566792,59.6480618 C76.5956048,67.5028569 74.3660182,76.7914798 74.0230569,85.4813651 C73.8884429,88.8932666 74.3484305,92.415154 75.267729,95.7003546 C75.6049405,96.9061873 77.4232441,101.086987 77.9224658,97.70885 C78.0885348,96.584253 77.4804043,95.0327505 77.4215529,93.8376154 C77.3444374,92.2693977 77.4273028,90.6807875 77.5341822,89.1149098 C77.7340739,86.1874141 78.2559568,83.3154127 79.1847257,80.524647 C80.5119249,76.5367405 82.2013644,72.212859 84.7874413,68.8480942 C91.1883705,60.5205939 102.268658,60.045883 111.066257,55.4635848 C110.302881,56.8686624 107.359637,59.1432635 108.379389,60.7268592 C109.083576,61.8207003 111.749474,61.4890712 113.022219,61.4539693 C116.370996,61.3623702 119.734993,60.7796791 123.042844,60.306974 C128.255923,59.5618115 133.140585,58.0517626 138.046555,56.2181078 C142.06299,54.7164165 146.650012,53.3257139 149.66868,50.1404701 C154.539813,55.1881187 160.809849,59.9345599 167.070414,63.1428706 C172.68835,66.0222267 181.748751,67.461069 185.182761,73.3006826 C189.2479,80.2147477 187.37751,88.7073939 188.619138,96.2008069 C189.091302,99.05041 190.164155,98.986558 190.751315,96.4378281 C191.748406,92.1082634 192.219217,87.6102098 191.901961,83.1592929 C191.183568,73.1114668 187.496636,46.7057251 167.308863,35.006118 Z" id="react-path-497"></path><filter x="-0.8%" y="-2.0%" width="101.5%" height="108.0%" filterUnits="objectBoundingBox" id="react-filter-494"><feOffset dx="0" dy="2" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset><feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.16 0" type="matrix" in="shadowOffsetOuter1" result="shadowMatrixOuter1"></feColorMatrix><feMerge><feMergeNode in="shadowMatrixOuter1"></feMergeNode><feMergeNode in="SourceGraphic"></feMergeNode></feMerge></filter></defs><mask id="react-mask-496" fill="white"><use xlink:href="#react-path-498"></use></mask><g id="Mask"></g><g id="Top/Short-Hair/Short-Round" mask="url(#react-mask-496)"><g transform="translate(-1.000000, 0.000000)"><g id="Facial-Hair/Moustache-Fancy" transform="translate(49.000000, 72.000000)"><defs><path d="M84.0002865,69.2970648 C77.2083681,65.7112456 67.5782013,65.1489138 62.3885276,67.1316942 C56.6144416,69.3374281 51.5052994,75.5829845 42.6388201,72.8283797 C42.2699314,72.7136458 41.9094725,73.0449523 42.0204089,73.408662 C43.3937943,77.9183313 51.0278347,81.0068878 53.6221945,81.1080652 C64.961124,81.549609 74.0949802,72.8302891 84.0002865,72.1614794 C93.9055921,72.8302891 103.03945,81.549609 114.378714,81.1080652 C116.972736,81.0068878 124.607113,77.9183313 125.980498,73.408662 C126.091098,73.0449523 125.730639,72.7136458 125.36175,72.8283797 C116.495271,75.5829845 111.386129,69.3374281 105.612044,67.1316942 C100.422371,65.1489138 90.7922044,65.7112456 84.0002865,69.2970648 Z" id="react-path-500"></path></defs><mask id="react-mask-499" fill="white"><use xlink:href="#react-path-500"></use></mask><use id="Moustache-U-a-Question" fill="#28354B" fill-rule="evenodd" xlink:href="#react-path-500"></use><g id="Color/Hair/Brown" mask="url(#react-mask-499)" fill="#2C1B18"><g transform="translate(-32.000000, 0.000000)" id="Color"><rect x="0" y="0" width="264" height="244"></rect></g></g></g><mask id="react-mask-495" fill="white"><use xlink:href="#react-path-497"></use></mask><use id="Short-Hair" stroke="none" fill="#28354B" fill-rule="evenodd" xlink:href="#react-path-497"></use><g id="Skin/👶🏽-03-Brown" mask="url(#react-mask-495)" fill="#D6B370"><g transform="translate(0.000000, 0.000000) " id="Color"><rect x="0" y="0" width="264" height="280"></rect></g></g><g id="Top/_Resources/Round" fill="none" transform="translate(62.000000, 85.000000)"><defs><path d="M80.4646192,16.3941179 C84.3801509,8.45869677 92.552602,3 102,3 C110.919691,3 118.702887,7.86591082 122.839921,15.0880638 C123.050197,15.0306504 123.271519,15 123.5,15 L131.5,15 C132.880712,15 134,16.1192881 134,17.5 C134,18.8807119 132.880712,20 131.5,20 L124.963126,20 C125.637355,22.2145921 126,24.5649459 126,27 C126,40.254834 115.254834,51 102,51 C88.745166,51 78,40.254834 78,27 C78,25.5781066 78.1236513,24.1850936 78.3607874,22.8311273 C78.2762458,18.4553035 74.9135957,15 70.8624171,15 C67.1256697,15 63.9747186,17.9397535 63.4417635,21.8300629 C63.8073299,23.4951922 64,25.2250958 64,27 C64,40.254834 53.254834,51 40,51 C26.745166,51 16,40.254834 16,27 C16,24.5649459 16.3626451,22.2145921 17.0368738,20 L10.5,20 C9.11928813,20 8,18.8807119 8,17.5 C8,16.1192881 9.11928813,15 10.5,15 L10.5,15 L18.5,15 C18.728481,15 18.9498033,15.0306504 19.1600793,15.0880638 C23.2971127,7.86591082 31.0803092,3 40,3 C49.3521568,3 57.4549431,8.34919095 61.415666,16.15488 C63.4929212,13.0392725 66.9494432,11 70.8624171,11 C74.8746823,11 78.4070368,13.1440781 80.4646192,16.3941179 Z M40,47 C51.045695,47 60,38.045695 60,27 C60,15.954305 51.045695,7 40,7 C28.954305,7 20,15.954305 20,27 C20,38.045695 28.954305,47 40,47 Z M102,47 C113.045695,47 122,38.045695 122,27 C122,15.954305 113.045695,7 102,7 C90.954305,7 82,15.954305 82,27 C82,38.045695 90.954305,47 102,47 Z" id="react-path-501"></path><filter x="-0.8%" y="-2.1%" width="101.6%" height="108.3%" filterUnits="objectBoundingBox" id="react-filter-502"><feOffset dx="0" dy="2" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset><feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.1 0" type="matrix" in="shadowOffsetOuter1"></feColorMatrix></filter></defs><g id="Lennon-Glasses"><use fill="black" fill-opacity="1" filter="url(#react-filter-502)" xlink:href="#react-path-501"></use><use fill="#252C2F" fill-rule="evenodd" xlink:href="#react-path-501"></use></g></g></g></g></g></g></g></g></g></svg>