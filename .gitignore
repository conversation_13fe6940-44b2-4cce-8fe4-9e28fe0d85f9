# Compiled Object files, Static and Dynamic libs (Shared Objects)
*.o
*.a
*.so

# Folders
_obj
_test
web/*


.vscode
#!.vscode/settings.json
#!.vscode/tasks.json
#!.vscode/launch.json

# Architecture specific extensions/prefixes
*.[568vq]
[568vq].out

*.cgo1.go
*.cgo2.c
_cgo_defun.c
_cgo_gotypes.go
_cgo_export.*

_testmain.go

*.exe
*.test
*.prof

# external packages folder
vendor/
webserver/


cmd/fetch.d/fetch.d
cmd/fetch.d/debug
cmd/fetch.d/diskv
cmd/parse.d/parse.d
cmd/parse.d/debug
cmd/parse.d/diskv
diskv
.deploy
deploy.sh
socket-server/debug
socket-server/socket-server
parseNATS/debug
parseNATS/parseNATS
webserver/webserver
coverage.txt
coverage.out

storage/mongo
storage/cassandra/db

webserver
examples/diesel*
examples/najnakup.sk.json

img_src
.vscode/launch.json
testserver/testserver
profile.out
output/
results/
fetch/<autogenerated>
cmd/fetch.cli/fetch.cli
node_modules

fetch/urls
fetch/urls2
