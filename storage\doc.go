// Dataflow kit - storage
//
// Copyright © 2017-2018 Slotix s.r.o. <<EMAIL>>
//
//
// All rights reserved. Use of this source code is governed
// by the BSD 3-Clause License license.

// Package storage of the Dataflow kit describes Store interface for read/ write operations with downloaded data and parsed results.
//
//Storage types like Diskv, MongoDB implement methods which satisfy Store interface.
//
package storage

// EOF
