version: '3'

services:
  chrome:
    image: yukinying/chrome-headless-browser
    cap_add:
      - SYS_ADMIN
    entrypoint: /usr/bin/dumb-init -- /usr/bin/google-chrome-unstable --disable-gpu --headless --disable-dev-shm-usage --remote-debugging-address=0.0.0.0 --remote-debugging-port=9222 #--proxy-server=XX.YY.ZZ.TT:8888 --proxy-bypass-list=127.0.0.1 --proxy-bypass-list=0.0.0.0 --user-data-dir=/data --blink-settings=imagesEnabled=false
    ports:
      - "9222:9222"


  mongo:
    image: mvertes/alpine-mongo
    ports: 
      - "27017:27017"
    volumes:
      - ./storage/mongo/db:/data/db

  testserver:
    #build: ./testserver
    image: slotix/dfk-testserver
    environment:
      - DFK_TEST=testserver:12345
    ports:
      - "12345:12345"
    depends_on:
      - chrome
  