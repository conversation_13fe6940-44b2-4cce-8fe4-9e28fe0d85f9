{{define "content"}}
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item">
                <a href="/">Home</a>
            </li>
            <li class="breadcrumb-item">
                <a href="/countries">By countries</a>
            </li>
            <li class="breadcrumb-item">
                <a href="/country/{{ .Country }}">{{ .Country }}</a>
            </li>
            <li class="breadcrumb-item active" aria-current="page">{{ .City }} - {{ .TotalPersons }} persons</li>
        </ol>
    </nav>

    <nav aria-label="Person Pages">
        <ul class="pagination justify-content-center">
            {{if or (eq .CurrentPage 1) (lt .CurrentPage 1)}}
                <li class="page-item disabled">
                    <span class="page-link">&laquo;</span>
                </li>
            {{ else }}
                <li class="page-item">
                    <a class="page-link" href="/country/{{ .Country }}/city/{{ .City }}/page/{{ dec .CurrentPage }}">&laquo;</a>
                </li>
            {{end}}
            {{ range $i, $value := (seqPages .TotalPages) }}
                    <li class="page-item {{if eq $value $.CurrentPage}}active{{end}}"><a href="/country/{{$.Country}}/city/{{$.City}}{{ if ne $value 1 }}/page/{{ $value }}{{ end }}" class="page-link">{{ $value }}</a></li>
            {{ end }}
            {{if eq .CurrentPage .TotalPages}}
                <li class="page-item disabled">
                    <span class="page-link">&raquo;</span>
                </li>
            {{ else }}
                <li class="page-item">
                    <a class="page-link" href="/country/{{ .Country }}/city/{{ .City }}/page/{{ inc .CurrentPage }}">&raquo;</a>
                </li>
            {{end}}
        </ul>
    </nav>

    <div id="cards">
        <div class="card-columns">
            {{ range $index, $person := .Persons }}
            <div class="card">
                <img src="/static/img/avataaars-{{ $person.Counter }}.svg" class="card-img-top" width="264" height="280" alt="">
                <div class="card-body">
                    <h3 class="card-title"><span class="badge badge-pill badge-primary">{{ $person.Counter }}</span>&nbsp;&nbsp;<a href="/persons/{{ $person.Counter }}">{{ $person.Name }}</a></h3>
                </div>
            </div>
            {{ end }}
        </div>
    </div>
{{end}}