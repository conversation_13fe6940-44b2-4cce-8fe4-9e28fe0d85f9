{{define "content"}}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item">
            <a href="/">Home</a>
        </li>
        <li class="breadcrumb-item active" aria-current="page">Person cards</li>
    </ol>
</nav>
<nav aria-label="Page navigation example">
    <ul class="pagination justify-content-end">
        {{ if eq .Page 0 }}
        <li class="page-item disabled">
            <a class="page-link" href="#">Previous</a>
        </li>
        {{else}}
        <li class="page-item">
            <a class="page-link" href="/persons/page-{{dec .Page}}">Previous</a>
        </li>
        {{end}} {{ if eq .Page 9 }}
        <li class="page-item disabled">
            <a class="page-link" href="#">Next</a>
        </li>
        {{else}}
        <li class="page-item">
            <a class="page-link" href="/persons/page-{{inc .Page}}">Next</a>
        </li>
        {{end}}
    </ul>
</nav>
<div id="cards"></div>
<nav aria-label="Page navigation example">
    <ul class="pagination justify-content-end">
        {{ if eq .Page 0 }}
        <li class="page-item disabled">
            <a class="page-link" href="#">Previous</a>
        </li>
        {{else}}
        <li class="page-item">
            <a class="page-link" href="/persons/page-{{dec .Page}}">Previous</a>
        </li>
        {{end}} {{ if eq .Page 9 }}
        <li class="page-item disabled">
            <a class="page-link" href="#">Next</a>
        </li>
        {{else}}
        <li class="page-item">
            <a class="page-link" href="/persons/page-{{inc .Page}}">Next</a>
        </li>
        {{end}}
    </ul>
</nav>

<script>
    var count = {{.ItemsPerPage }}
    var page = {{.Page }}
    var mydata = {{.Data }};
    $('#cards').append('<div class="card-columns"></div>');
    for (var i = page * count; i < page * count + count; i++) {
        $('.card-columns').append(`
                    <div class="card">
                            <img src="/static/img/avataaars-${mydata[i]["Counter"]}.svg" class="card-img-top" width="264" height="280" alt="">
                            <div class="card-body">
                            
                            <h3 class="card-title"><span class="badge badge-pill badge-primary">${mydata[i]["Counter"]}</span>&nbsp;&nbsp;<a href="./${mydata[i]["Counter"]}">${mydata[i]["Name"]}</a></h3>
                        </div>
                    </div>`);
    }
</script> {{end}}