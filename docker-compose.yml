version: '3'

services:
  chrome:
    image: yukinying/chrome-headless-browser
    
    cap_add:
      - SYS_ADMIN
    entrypoint: /usr/bin/dumb-init -- /usr/bin/google-chrome-unstable --disable-gpu --headless --disable-dev-shm-usage --remote-debugging-address=0.0.0.0 --remote-debugging-port=9222 #--proxy-server=XX.YY.ZZ.TT:8888 --proxy-bypass-list=127.0.0.1 --proxy-bypass-list=0.0.0.0 --user-data-dir=/data --blink-settings=imagesEnabled=false
    ports:
      - "9222:9222"

  mongo:
    image: mvertes/alpine-mongo
    #image: mongo
    ports: 
      - 27017:27017
    volumes:
      - ./storage/mongo/db:/data/db
    # environment:
    #   - MONGO_INITDB_ROOT_USERNAME=root
    #   - MONGO_INITDB_ROOT_PASSWORD=password
  
  mongo-express:
    image: mongo-express
    restart: always
    ports:
      - 8081:8081
    # environment:
    #   ME_CONFIG_MONGODB_ADMINUSERNAME: root
    #   ME_CONFIG_MONGODB_ADMINPASSWORD: password

  fetch:
    image: slotix/dfk-fetch
    #build: ./cmd/fetch.d
    entrypoint:
      - ./fetch.d
      - --CHROME=http://chrome:9222
      - --STORAGE_TYPE=MongoDB
      - --MONGO=mongo
    environment:
      #- CHROME=chrome:9222
      - DFK_FETCH=fetch:8000
      - DISKV_BASE_DIR=./diskv
    volumes:
      - ./diskv:/diskv
      - ./cmd/fetch.d/chrome:/chrome
    ports:
      - "8000:8000"
    #env_file: 
     # - .env
    depends_on:
      - chrome
    restart: always
  
  parse:
    image: slotix/dfk-parse
    #build: ./cmd/parse.d
    entrypoint:
      - ./parse.d
      - --STORAGE_TYPE=MongoDB
      - --MONGO=mongo
    environment:
      - DFK_FETCH=fetch:8000
      - DFK_PARSE=parse:8001
      - DISKV_BASE_DIR=./diskv
      - RESULTS_DIR=./results
    volumes:
      - ./diskv:/diskv
      - ./results:/results
    ports:
      - "8001:8001"
    depends_on:
      - fetch
    restart: always
  
  # prometheus:
  #   image: prom/prometheus
  #   ports:
  #     - 9090:9090
  #   volumes:
  #     - $PWD/metrics/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml

  # node-exporter:
  #   image: prom/node-exporter
  #   volumes:
  #     - /proc:/host/proc:ro
  #     - /sys:/host/sys:ro
  #     - /:/rootfs:ro
  #   command: 
  #     - '--path.procfs=/host/proc' 
  #     - '--path.sysfs=/host/sys'
  #     - --collector.filesystem.ignored-mount-points
  #     - "^/(sys|proc|dev|host|etc|rootfs/var/lib/docker/containers|rootfs/var/lib/docker/overlay2|rootfs/run/docker/netns|rootfs/var/lib/docker/aufs)($$|/)"
  #   ports:
  #     - 9100:9100
  #   restart: always
    
  # grafana:
  #   image: grafana/grafana
  #   ports:
  #     - 3000:3000
  #   environment:
  #     - GF_SECURITY_ADMIN_PASSWORD=password
  #   volumes:
  #     - $PWD/metrics/grafana/grafana_db:/var/lib/grafana grafana/grafana
volumes:
  diskv:
  results: