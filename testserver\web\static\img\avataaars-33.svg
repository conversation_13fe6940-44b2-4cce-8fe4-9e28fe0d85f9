<svg width="264px" height="280px" viewBox="0 0 264 280" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><desc>Created with getavataaars.com</desc><defs><circle id="path-1" cx="120" cy="120" r="120"></circle><path d="M12,160 C12,226.27417 65.72583,280 132,280 C198.27417,280 252,226.27417 252,160 L264,160 L264,-1.42108547e-14 L-3.19744231e-14,-1.42108547e-14 L-3.19744231e-14,160 L12,160 Z" id="path-3"></path><path d="M124,144.610951 L124,163 L128,163 L128,163 C167.764502,163 200,195.235498 200,235 L200,244 L0,244 L0,235 C-4.86974701e-15,195.235498 32.235498,163 72,163 L72,163 L76,163 L76,144.610951 C58.7626345,136.422372 46.3722246,119.687011 44.3051388,99.8812385 C38.4803105,99.0577866 34,94.0521096 34,88 L34,74 C34,68.0540074 38.3245733,63.1180731 44,62.1659169 L44,56 L44,56 C44,25.072054 69.072054,5.68137151e-15 100,0 L100,0 L100,0 C130.927946,-5.68137151e-15 156,25.072054 156,56 L156,62.1659169 C161.675427,63.1180731 166,68.0540074 166,74 L166,88 C166,94.0521096 161.51969,99.0577866 155.694861,99.8812385 C153.627775,119.687011 141.237365,136.422372 124,144.610951 Z" id="path-5"></path></defs><g id="Avataaar" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd"><g transform="translate(-825.000000, -1100.000000)" id="Avataaar/Circle"><g transform="translate(825.000000, 1100.000000)"><g id="Circle" stroke-width="1" fill-rule="evenodd" transform="translate(12.000000, 40.000000)"><mask id="mask-2" fill="white"><use xlink:href="#path-1"></use></mask><use id="Circle-Background" fill="#E6E6E6" xlink:href="#path-1"></use><g id="Color/Palette/Blue-01" mask="url(#mask-2)" fill="#65C9FF"><rect id="🖍Color" x="0" y="0" width="240" height="240"></rect></g></g><mask id="mask-4" fill="white"><use xlink:href="#path-3"></use></mask><g id="Mask"></g><g id="Avataaar" stroke-width="1" fill-rule="evenodd" mask="url(#mask-4)"><g id="Body" transform="translate(32.000000, 36.000000)"><mask id="mask-6" fill="white"><use xlink:href="#path-5"></use></mask><use fill="#D0C6AC" xlink:href="#path-5"></use><g id="Skin/👶🏽-03-Brown" mask="url(#mask-6)" fill="#D08B5B"><g transform="translate(-32.000000, 0.000000)" id="Color"><rect x="0" y="0" width="264" height="244"></rect></g></g><path d="M156,79 L156,102 C156,132.927946 130.927946,158 100,158 C69.072054,158 44,132.927946 44,102 L44,79 L44,94 C44,124.927946 69.072054,150 100,150 C130.927946,150 156,124.927946 156,94 L156,79 Z" id="Neck-Shadow" fill-opacity="0.100000001" fill="#000000" mask="url(#mask-6)"></path></g><g id="Clothing/Graphic-Shirt" transform="translate(0.000000, 170.000000)"><defs><path d="M165.624032,29.2681342 C202.760022,32.1373245 232,63.1798426 232,101.051724 L232,110 L32,110 L32,101.051724 C32,62.8348009 61.7752018,31.5722494 99.3929298,29.1967444 C99.1342224,30.2735458 99,31.3767131 99,32.5 C99,44.3741221 113.998461,54 132.5,54 C151.001539,54 166,44.3741221 166,32.5 C166,31.4015235 165.871641,30.3222877 165.624025,29.2681336 Z" id="react-path-487"></path></defs><mask id="react-mask-488" fill="white"><use xlink:href="#react-path-487"></use></mask><use id="Clothes" fill="#E6E6E6" fill-rule="evenodd" xlink:href="#react-path-487"></use><g id="Color/Palette/Gray-01" mask="url(#react-mask-488)" fill-rule="evenodd" fill="#FFFFFF"><rect id="🖍Color" x="0" y="0" width="264" height="110"></rect></g><g id="Clothing/Graphic/Diamond" mask="url(#react-mask-488)" stroke-width="1" fill-rule="evenodd"><g transform="translate(77.000000, 58.000000)" id="Diamond"><g transform="translate(29.000000, 2.000000)"><path d="M34.7804613,27.7365051 C32.3910217,30.3933795 29.9226048,32.9801662 27.4446301,35.5565142 C28.4300853,32.0044048 29.1846452,27.9785814 30.6489944,24.5994547 C31.0655115,23.6371144 30.7485963,23.5153306 31.576097,23.1057395 C32.1847753,22.8045111 33.6536519,23.1494823 34.3825567,23.1678741 C35.9404713,23.2066461 37.5335988,23.3597457 39.0789374,23.1186635 C37.6281703,24.6436945 36.1869609,26.1712109 34.7804613,27.7365051 M19.5162179,31.798118 C16.8174087,28.5164183 14.1321816,25.2302449 10.8578947,22.4814113 C12.146683,22.6176103 13.5536857,22.4376686 14.8163159,22.687201 C16.7595591,23.0714412 16.6534177,23.6778747 17.6358547,25.6458009 C19.0866218,28.5522078 20.5248129,31.434755 21.6858291,34.4689106 C20.9750337,33.5692019 20.2446197,32.6844056 19.5162179,31.798118 M10.4182378,19.266817 C11.3810563,17.6920783 11.5616476,16.294796 13.3373785,15.4865494 C15.3988361,14.5480688 18.6379102,14.7454082 20.8256308,14.5709343 C19.4553501,16.093977 17.7887788,17.6736865 17.2223558,19.6928116 C16.3480725,17.9749149 15.2599971,16.5189775 13.5521766,15.5223389 C13.3368755,15.4159645 14.7101745,20.0725781 14.8897597,20.3926954 C13.1743936,20.2967597 11.3684803,19.9850926 9.66518708,20.3012334 C9.916204,19.9562622 10.1672209,19.611291 10.4182378,19.266817 M26.8243819,14.2592673 C28.4154971,14.2945597 29.9940364,14.4183318 31.563521,14.6783029 C30.2968665,16.353451 29.9150592,18.3104416 29.4653415,20.3086895 C28.9869505,18.0509676 27.2564932,15.9473394 25.7312761,14.2607585 C26.095477,14.2602615 26.4596779,14.2597644 26.8243819,14.2592673 M23.3257392,15.3334498 C23.5601558,15.1748824 27.7826729,20.4190405 28.3923573,20.889275 C25.0798394,20.8813217 21.7743641,20.7192748 18.4658705,20.5736313 C20.3728949,19.0868752 21.4886374,16.8863171 23.3257392,15.3334498 M26.0124754,32.4289082 C25.6105465,33.7779741 25.2352788,35.1369815 24.797634,36.4756088 C23.2432407,31.7444337 20.9357965,27.288142 18.7898282,22.8020257 C22.1295103,22.8900083 25.465671,23.0107979 28.8058561,23.0575231 C27.9864041,26.2164449 26.9441054,29.3032906 26.0124754,32.4289082 M33.2416622,14.9656131 C34.5188805,16.093977 36.338879,18.4063774 36.9284418,19.1833081 C36.9867944,19.260355 37.0461531,19.3369048 37.1070209,19.4124604 C37.3177947,19.6744198 38.0733606,20.6680759 38.3701542,21.0567898 C36.1426934,20.8306199 33.8251885,20.9270528 31.5901821,20.9066727 C32.0881916,18.8527521 32.6938517,16.8952645 32.1737084,14.7787123 C32.5308668,14.8363732 32.886516,14.8985078 33.2416622,14.9656131 M40.8898811,20.5830758 C38.7323429,17.9068154 36.5199733,13.3247633 33.025858,12.5055811 C29.5538765,11.6918667 25.478247,12.0517501 21.9519372,12.2754346 C18.8773572,12.4702886 13.8690917,12.0721303 11.1572035,13.8690622 C9.76378291,14.7921334 8.73154499,16.8738902 8.2491297,18.4202955 C7.81450321,19.8131041 8.51523782,20.7048596 7.00259677,20.7048596 C6.90651615,20.7048596 9.50672951,24.3777585 9.81308082,24.66656 C12.0853121,26.8064748 14.2574385,28.8688456 16.2701013,31.2642577 C19.1047312,34.6379166 21.6747622,39.6022206 25.414864,41 C24.7292206,40.6609938 28.2067356,38.0334469 28.6242587,37.5811072 C30.7355173,35.2930636 32.8709217,33.0268914 34.9967685,30.7517718 C36.9349813,28.6779682 39.0472459,26.7209776 40.9271061,24.6004489 C42.361776,22.9824645 42.3658003,22.4262358 40.8898811,20.5830758" id="Fill-21" fill="#FFFFFF"></path><path d="M6.99858316,11.9999257 C7.04159508,12.0142408 6.09038895,9.95483812 6.17394084,10.0812061 C5.69932656,9.36248835 5.12830624,8.8402333 4.43566602,8.35351923 C3.70792411,7.84261755 0.1270582,7.25421678 0,7 C1.49355183,9.65224611 4.16721231,11.1276907 6.99858316,11.9999257" id="Fill-23" fill="#FFFFFF"></path><path d="M22.3553325,7.19614224 C22.5279973,7.84840006 22.7925782,8.44932951 23.1490753,8.99893058 C23.2186566,9.07737584 24.0076748,4.81518326 23.9999436,4.45201075 C23.9866286,3.8317121 23.6120919,0 22.814054,0 C21.4769753,0 22.1397162,6.46931298 22.3553325,7.19614224" id="Fill-25" fill="#FFFFFF"></path><path d="M48.969198,5 C45.977552,5 39.2896081,10.8535875 40.0615528,13.9997703 C40.0673207,14.0103781 40.50905,13.6497123 40.4129174,13.7172166 C40.4321439,13.7032335 40.4508897,13.6887683 40.4691549,13.6738209 C41.7405084,12.649202 43.1873037,11.6544779 44.4605798,10.4683308 C45.3723974,9.61874094 46.4476403,8.85642448 47.2772645,7.92727594 C47.3618612,7.83276992 49.2628831,5 48.969198,5" id="Fill-27" fill="#FFFFFF"></path></g></g></g></g><g id="Face" transform="translate(76.000000, 82.000000)" fill="#000000"><g id="Mouth/Eating" transform="translate(2.000000, 52.000000)"><g id="Om-Nom-Nom" opacity="0.599999964" stroke-width="1" transform="translate(28.000000, 6.000000)" fill-opacity="0.599999964" fill="#000000"><path d="M16.1906378,10.106319 C16.0179484,4.99553347 11.7923466,0.797193688 6.29352385,0 C9.66004124,1.95870633 11.9804619,5.49520667 11.9804619,9.67694348 C11.9804619,15.344608 6.50694731,20.2451296 0.176591694,20.2451296 C0.11761218,20.2451296 0.0587475828,20.2447983 0,20.244138 L8.8963743e-11,20.244138 C1.35764479,20.7317259 2.83995964,21 4.39225962,21 C9.71395931,21 14.2131224,17.8469699 15.6863572,13.5136402 C18.1609431,15.6698775 21.8629994,17.0394229 26,17.0394229 C30.1370006,17.0394229 33.8390569,15.6698775 36.3136428,13.5136402 C37.7868776,17.8469699 42.2860407,21 47.6077404,21 C49.1600404,21 50.6423552,20.7317259 52,20.244138 L52,20.244138 C51.9412524,20.2447983 51.8823878,20.2451296 51.8234083,20.2451296 C45.4930527,20.2451296 40.0195381,15.344608 40.0195381,9.67694348 C40.0195381,5.49520667 42.3399588,1.95870633 45.7064761,0 C40.2076534,0.797193688 35.9820516,4.99553347 35.8093622,10.106319 C33.2452605,11.8422828 29.7948543,12.9056086 26,12.9056086 C22.2051457,12.9056086 18.7547395,11.8422828 16.1906378,10.106319 Z" id="Delicious"></path></g><circle id="Redish" fill-opacity="0.2" fill="#FF4646" cx="17" cy="15" r="9"></circle><circle id="Redish" fill-opacity="0.2" fill="#FF4646" cx="91" cy="15" r="9"></circle></g><g id="Nose/Default" transform="translate(28.000000, 40.000000)" fill-opacity="0.16"><path d="M16,8 C16,12.418278 21.372583,16 28,16 L28,16 C34.627417,16 40,12.418278 40,8" id="Nose"></path></g><g id="Eyes/Closed-😌" transform="translate(0.000000, 8.000000)" fill-opacity="0.599999964"><path d="M16.1601674,32.4473116 C18.006676,28.648508 22.1644225,26 26.9975803,26 C31.8136766,26 35.9591217,28.629842 37.8153518,32.4071242 C38.3667605,33.5291977 37.5821037,34.4474817 36.790607,33.7670228 C34.3395063,31.6597833 30.8587163,30.3437884 26.9975803,30.3437884 C23.2572061,30.3437884 19.8737584,31.5787519 17.4375392,33.5716412 C16.5467928,34.3002944 15.6201012,33.5583844 16.1601674,32.4473116 Z" id="Closed-Eye" transform="translate(27.000000, 30.000000) scale(1, -1) translate(-27.000000, -30.000000) "></path><path d="M74.1601674,32.4473116 C76.006676,28.648508 80.1644225,26 84.9975803,26 C89.8136766,26 93.9591217,28.629842 95.8153518,32.4071242 C96.3667605,33.5291977 95.5821037,34.4474817 94.790607,33.7670228 C92.3395063,31.6597833 88.8587163,30.3437884 84.9975803,30.3437884 C81.2572061,30.3437884 77.8737584,31.5787519 75.4375392,33.5716412 C74.5467928,34.3002944 73.6201012,33.5583844 74.1601674,32.4473116 Z" id="Closed-Eye" transform="translate(85.000000, 30.000000) scale(1, -1) translate(-85.000000, -30.000000) "></path></g><g id="Eyebrow/Outline/Default" fill-opacity="0.599999964"><g id="I-Browse" transform="translate(12.000000, 6.000000)"><path d="M3.63024536,11.1585767 C7.54515501,5.64986673 18.2779197,2.56083721 27.5230268,4.83118046 C28.5957248,5.0946055 29.6788665,4.43856013 29.9422916,3.36586212 C30.2057166,2.2931641 29.5496712,1.21002236 28.4769732,0.94659732 C17.7403633,-1.69001789 5.31209962,1.88699832 0.369754639,8.84142326 C-0.270109626,9.74178291 -0.0589363917,10.9903811 0.84142326,11.6302454 C1.74178291,12.2701096 2.9903811,12.0589364 3.63024536,11.1585767 Z" id="Eyebrow" fill-rule="nonzero"></path><path d="M61.6302454,11.1585767 C65.545155,5.64986673 76.2779197,2.56083721 85.5230268,4.83118046 C86.5957248,5.0946055 87.6788665,4.43856013 87.9422916,3.36586212 C88.2057166,2.2931641 87.5496712,1.21002236 86.4769732,0.94659732 C75.7403633,-1.69001789 63.3120996,1.88699832 58.3697546,8.84142326 C57.7298904,9.74178291 57.9410636,10.9903811 58.8414233,11.6302454 C59.7417829,12.2701096 60.9903811,12.0589364 61.6302454,11.1585767 Z" id="Eyebrow" fill-rule="nonzero" transform="translate(73.000154, 6.039198) scale(-1, 1) translate(-73.000154, -6.039198) "></path></g></g></g><g id="Top" stroke-width="1" fill-rule="evenodd"><defs><rect id="react-path-492" x="0" y="0" width="264" height="280"></rect><path d="M1,64 C1.34685629,65.488448 2.67275588,65.2226722 3,64 C2.53726005,62.445722 6.29594493,35.2480719 16,28 C19.618222,25.4833872 39.0082164,23.2319099 58.3126144,23.245568 C77.4086061,23.2590787 96.4208396,25.5105561 100,28 C109.704055,35.2480719 113.46274,62.445722 113,64 C113.327244,65.2226722 114.653144,65.488448 115,64 C115.719178,53.7019177 115,0.274362825 58,1 C1,1.72563718 0.280821545,53.7019177 1,64 Z" id="react-path-493"></path><filter x="-0.8%" y="-2.0%" width="101.5%" height="108.0%" filterUnits="objectBoundingBox" id="react-filter-489"><feOffset dx="0" dy="2" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset><feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.16 0" type="matrix" in="shadowOffsetOuter1" result="shadowMatrixOuter1"></feColorMatrix><feMerge><feMergeNode in="shadowMatrixOuter1"></feMergeNode><feMergeNode in="SourceGraphic"></feMergeNode></feMerge></filter></defs><mask id="react-mask-490" fill="white"><use xlink:href="#react-path-492"></use></mask><g id="Mask"></g><g id="Top/Short-Hair/The-Caesar" mask="url(#react-mask-490)"><g transform="translate(-1.000000, 0.000000)"><g id="Hair" stroke-width="1" fill-rule="evenodd" transform="translate(75.000000, 34.000000)"><mask id="react-mask-491" fill="white"><use xlink:href="#react-path-493"></use></mask><use id="Caesar" fill="#28354B" xlink:href="#react-path-493"></use><g id="Skin/👶🏽-03-Brown" mask="url(#react-mask-491)" fill="#F59797"><g transform="translate(0.000000, 0.000000) " id="Color"><rect x="0" y="0" width="264" height="280"></rect></g></g></g></g></g></g></g></g></g></g></svg>