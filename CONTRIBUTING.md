# Contributing

Thank you for contributing! We love and encourage pull requests from everyone.

Before submitting your changes, here are a few guidelines to follow:

1. Check the [open issues][issues] and [pull requests][pr] for existing discussions.
1. Open an [issue][issues] first, to discuss a new feature or enhancement.
1. Write tests, and make sure the test suite passes locally and on CI.
1. Open a pull request, and reference the relevant issue(s).
1. After receiving feedback, push your commit and add a commit message.
1. Have fun!

[issues]: https://github.com/slotix/dataflowkit/issues
[pr]: https://github.com/slotix/dataflowkit/pulls
