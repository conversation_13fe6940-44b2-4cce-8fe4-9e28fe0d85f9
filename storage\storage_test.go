package storage

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestNewStore(t *testing.T) {

	for _, sType := range []string{ /*"S3", "Spaces",*/ "Diskv", "mongodb"} {
		store := NewStore(sType)
		assert.NotNil(t, store)
	}
}

func TestInvalidStore(t *testing.T) {
	sType := "unknownStorage"
	defer func() {
		if r := recover(); r == nil {
			t.<PERSON><PERSON>("The code did not panic")
		}
	}()
	store := NewStore(sType)
	assert.NotNil(t, store)
}
