<svg width="264px" height="280px" viewBox="0 0 264 280" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><desc>Created with getavataaars.com</desc><defs><circle id="path-1" cx="120" cy="120" r="120"></circle><path d="M12,160 C12,226.27417 65.72583,280 132,280 C198.27417,280 252,226.27417 252,160 L264,160 L264,-1.42108547e-14 L-3.19744231e-14,-1.42108547e-14 L-3.19744231e-14,160 L12,160 Z" id="path-3"></path><path d="M124,144.610951 L124,163 L128,163 L128,163 C167.764502,163 200,195.235498 200,235 L200,244 L0,244 L0,235 C-4.86974701e-15,195.235498 32.235498,163 72,163 L72,163 L76,163 L76,144.610951 C58.7626345,136.422372 46.3722246,119.687011 44.3051388,99.8812385 C38.4803105,99.0577866 34,94.0521096 34,88 L34,74 C34,68.0540074 38.3245733,63.1180731 44,62.1659169 L44,56 L44,56 C44,25.072054 69.072054,5.68137151e-15 100,0 L100,0 L100,0 C130.927946,-5.68137151e-15 156,25.072054 156,56 L156,62.1659169 C161.675427,63.1180731 166,68.0540074 166,74 L166,88 C166,94.0521096 161.51969,99.0577866 155.694861,99.8812385 C153.627775,119.687011 141.237365,136.422372 124,144.610951 Z" id="path-5"></path></defs><g id="Avataaar" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd"><g transform="translate(-825.000000, -1100.000000)" id="Avataaar/Circle"><g transform="translate(825.000000, 1100.000000)"><g id="Circle" stroke-width="1" fill-rule="evenodd" transform="translate(12.000000, 40.000000)"><mask id="mask-2" fill="white"><use xlink:href="#path-1"></use></mask><use id="Circle-Background" fill="#E6E6E6" xlink:href="#path-1"></use><g id="Color/Palette/Blue-01" mask="url(#mask-2)" fill="#65C9FF"><rect id="🖍Color" x="0" y="0" width="240" height="240"></rect></g></g><mask id="mask-4" fill="white"><use xlink:href="#path-3"></use></mask><g id="Mask"></g><g id="Avataaar" stroke-width="1" fill-rule="evenodd" mask="url(#mask-4)"><g id="Body" transform="translate(32.000000, 36.000000)"><mask id="mask-6" fill="white"><use xlink:href="#path-5"></use></mask><use fill="#D0C6AC" xlink:href="#path-5"></use><g id="Skin/👶🏽-03-Brown" mask="url(#mask-6)" fill="#614335"><g transform="translate(-32.000000, 0.000000)" id="Color"><rect x="0" y="0" width="264" height="244"></rect></g></g><path d="M156,79 L156,102 C156,132.927946 130.927946,158 100,158 C69.072054,158 44,132.927946 44,102 L44,79 L44,94 C44,124.927946 69.072054,150 100,150 C130.927946,150 156,124.927946 156,94 L156,79 Z" id="Neck-Shadow" fill-opacity="0.100000001" fill="#000000" mask="url(#mask-6)"></path></g><g id="Clothing/Graphic-Shirt" transform="translate(0.000000, 170.000000)"><defs><path d="M165.624032,29.2681342 C202.760022,32.1373245 232,63.1798426 232,101.051724 L232,110 L32,110 L32,101.051724 C32,62.8348009 61.7752018,31.5722494 99.3929298,29.1967444 C99.1342224,30.2735458 99,31.3767131 99,32.5 C99,44.3741221 113.998461,54 132.5,54 C151.001539,54 166,44.3741221 166,32.5 C166,31.4015235 165.871641,30.3222877 165.624025,29.2681336 Z" id="react-path-125"></path></defs><mask id="react-mask-126" fill="white"><use xlink:href="#react-path-125"></use></mask><use id="Clothes" fill="#E6E6E6" fill-rule="evenodd" xlink:href="#react-path-125"></use><g id="Color/Palette/Gray-01" mask="url(#react-mask-126)" fill-rule="evenodd" fill="#FF5C5C"><rect id="🖍Color" x="0" y="0" width="264" height="110"></rect></g><g id="Clothing/Graphic/Hola" mask="url(#react-mask-126)" fill-rule="evenodd" fill="#FFFFFF"><g transform="translate(77.000000, 58.000000)" id="Hola"><path d="M74.6371722,16.9479681 C75.917206,22.7098952 73.7328313,28.1917851 69.3072818,31.9519351 C67.2267705,33.7200766 64.8728066,35.0262382 62.2514759,35.7865716 C60.7921156,36.2102906 59.2674351,36.5090512 57.7541146,36.6572104 C56.61811,36.7683298 54.9700918,36.2570992 53.9631048,36.5456839 C52.8441403,36.8672382 51.4050658,38.4111058 50.2065809,38.9084974 C49.535121,39.1873134 48.8385068,39.4319389 48.1390525,39.6350472 C46.6326292,40.0734193 45.0742742,40.3689236 43.5256565,40.611514 C41.9242956,40.8622449 35.2433712,41.9140123 36.070626,38.9752504 C36.3550329,37.9641861 39.1816559,36.4707903 39.7950984,35.791049 C40.2486888,35.288773 42.9398027,32.427347 42.8140307,31.4960606 C42.7742705,31.2021844 41.0666121,29.8650885 40.6657648,29.4157265 C39.7573668,28.3973356 38.9041462,27.3581862 38.4205328,26.0646425 C37.5920608,23.8487672 37.6354724,21.0667121 37.9105478,18.7446017 C38.5986421,12.9407505 41.476791,9.08616941 46.5133487,6.19584415 C49.3679661,4.55795247 52.4720988,3.65678642 55.718232,3.26074551 C59.6739625,2.77841409 62.8279982,2.76294692 66.1848919,5.01097768 C70.6051671,7.97090519 73.4601902,11.6785482 74.6371722,16.9479681 Z M69.5056769,23.9358717 C70.5917785,23.6358901 69.1977385,20.8827342 68.427284,22.2161669 C68.1497742,22.6968702 68.8902058,24.1047895 69.5056769,23.9358717 Z M67.6848238,18.6143519 C67.7728641,18.973353 68.1185341,19.0836583 68.4325583,19.0405131 C69.182727,18.9371273 68.7125023,18.059976 68.7340052,17.6684125 C68.7741711,16.9194759 68.8407085,15.0292252 68.5619816,14.3474487 C68.0491567,13.0942011 67.5866405,13.3339422 67.2884393,14.5615469 C67.0210725,15.6617509 67.4109655,17.5092635 67.6848238,18.6143519 Z M66.6372652,22.892652 C67.4795315,22.3952604 65.2570196,16.7171816 64.7685376,15.9051553 C62.8770898,12.7604359 62.2652702,18.3363499 61.9751833,19.714149 C61.8498171,20.310856 60.4574,26.4960951 62.3135504,24.5020515 C63.3047144,23.4376661 61.755691,19.900162 64.5567538,20.7227711 C65.540615,21.0117629 64.8289892,20.9287287 65.4314774,21.8315228 C65.6517812,22.1616248 65.8887193,23.3326522 66.6372652,22.892652 Z M60.2947079,21.5987013 C61.0809854,20.2481733 57.6303713,20.8778498 57.2327696,20.9022717 C57.3268957,19.1027888 57.6782457,17.2792911 57.6733771,15.4798082 C57.67216,15.0052104 57.8380978,13.2887618 56.7146704,14.0987529 C56.2606742,14.4264127 56.4168749,15.9283561 56.3787376,16.3789391 C56.2886686,17.436405 55.4313908,21.0858425 56.2708171,21.9381649 C56.8660024,22.5421985 59.9640494,22.1587756 60.2947079,21.5987013 Z M55.4961584,19.1587406 C55.8337431,17.8148148 55.4118876,14.9057989 53.6512301,14.8794474 C53.0382604,14.8705068 53.3156518,15.7104604 53.164165,15.7984555 C52.0992431,16.4200683 51.6357538,16.3414844 51.0047261,17.4454234 C48.5177346,21.7957715 54.0705776,24.8897184 55.4961584,19.1587406 Z M49.4685836,22.9899323 C50.2301124,22.7799045 50.0146773,13.0115738 49.1375194,13.0062824 C47.6286618,12.9977348 49.0750392,17.8462849 48.5033854,18.49672 C48.3321733,18.6916877 47.4769241,18.5109661 47.2529689,18.5520762 C46.1129071,18.7637322 45.3623326,18.6444722 45.0126054,17.9464145 C44.7014213,17.3265067 45.3736926,13.0661159 43.7552917,15.1562188 C43.7698975,15.8937585 43.7816632,16.6317052 43.7901833,17.3692449 C44.1261161,18.0843979 44.0007498,18.5195137 43.4136789,18.6754065 C43.3844673,19.14512 43.8056005,19.5387187 43.8108748,19.6921693 C43.8360292,20.4630855 43.3268557,23.9232538 44.7111585,23.7563712 C45.5960249,23.6493221 45.0316741,20.2746303 45.0280226,19.7255458 C46.1705187,19.7532239 47.3673807,19.8806245 48.5033854,19.7336864 C48.5086597,20.1610687 48.2149214,23.3367225 49.4685836,22.9899323 Z M63.8368644,17 C64.2706568,17.6069046 64.6557203,18.2184508 65,18.8421816 C64.3342161,18.8990426 63.6679025,18.9518422 63,19 C63.2722458,18.332463 63.5439619,17.664926 63.8368644,17 Z M53.9230169,17 C54.0684325,17.4229772 54.1789882,20.2877939 52.5679624,19.9763934 C51.6476608,19.7985469 52.0614978,18.2121289 52.3219511,17.8848372 C52.8722396,17.1947521 53.1949428,17.4368397 53.9230169,17 Z"></path></g></g></g><g id="Face" transform="translate(76.000000, 82.000000)" fill="#000000"><g id="Mouth/Grimace" transform="translate(2.000000, 52.000000)"><defs><rect id="react-path-127" x="24" y="9" width="60" height="22" rx="11"></rect></defs><rect id="Mouth" fill-opacity="0.599999964" fill="#000000" fill-rule="evenodd" x="22" y="7" width="64" height="26" rx="13"></rect><mask id="react-mask-128" fill="white"><use xlink:href="#react-path-127"></use></mask><use id="Mouth" fill="#FFFFFF" fill-rule="evenodd" xlink:href="#react-path-127"></use><path d="M71,22 L62,22 L62,34 L58,34 L58,22 L49,22 L49,34 L45,34 L45,22 L36,22 L36,34 L32,34 L32,22 L24,22 L24,18 L32,18 L32,6 L36,6 L36,18 L45,18 L45,6 L49,6 L49,18 L58,18 L58,6 L62,6 L62,18 L71,18 L71,6 L75,6 L75,18 L83.8666667,18 L83.8666667,22 L75,22 L75,34 L71,34 L71,22 Z" id="Grimace-Teeth" fill="#E6E6E6" fill-rule="evenodd" mask="url(#react-mask-128)"></path></g><g id="Nose/Default" transform="translate(28.000000, 40.000000)" fill-opacity="0.16"><path d="M16,8 C16,12.418278 21.372583,16 28,16 L28,16 C34.627417,16 40,12.418278 40,8" id="Nose"></path></g><g id="Eyes/Eye-Roll-🙄" transform="translate(0.000000, 8.000000)"><circle id="Eyeball" fill="#FFFFFF" cx="30" cy="22" r="14"></circle><circle id="The-white-stuff" fill="#FFFFFF" cx="82" cy="22" r="14"></circle><circle id="Eye" fill-opacity="0.699999988" fill="#000000" cx="30" cy="14" r="6"></circle><circle id="Eye" fill-opacity="0.699999988" fill="#000000" cx="82" cy="14" r="6"></circle></g><g id="Eyebrow/Natural/Unibrow-Natural" fill-opacity="0.599999964"><path d="M57.000525,12 C56.999825,11.9961 56.999825,11.9961 57.000525,12 M59.4596631,14.892451 C61.3120123,16.058698 64.1131185,16.7894891 65.7030886,17.0505179 C71.9486685,18.0766191 78.0153663,15.945512 84.1715051,15.0153209 C89.636055,14.1895424 95.8563653,13.4967455 100.86041,16.507708 C100.987756,16.584232 101.997542,17.2147893 102.524546,17.7511372 C102.91024,18.1443003 103.563259,18.0619945 103.822605,17.5722412 C105.241692,14.8939029 97.7243204,8.76008291 96.2812935,8.14993193 C89.7471082,5.39200867 81.0899445,8.32440654 74.4284137,9.38927986 C70.6888462,9.98718701 66.9279989,10.3803501 63.2409655,11.2908151 C61.9188284,11.6171635 60.6278928,12.2066818 59.3382119,12.3724317 C59.1848981,12.1429782 58.9889964,12 58.7633758,12 C57.5922879,12 55.8451696,15.4574504 58.0750241,15.6547468 C58.7728345,15.7164887 59.215997,15.3816732 59.4596631,14.892451 Z" id="Kahlo" transform="translate(80.500000, 12.500000) rotate(-2.000000) translate(-80.500000, -12.500000) "></path><path d="M54.999475,12 C55.000175,11.9961 55.000175,11.9961 54.999475,12 M15.7187065,8.14993193 C22.2528918,5.39200867 30.9100555,8.32440654 37.5715863,9.38927986 C41.3111538,9.98718701 45.0720011,10.3803501 48.7590345,11.2908151 C50.2416282,11.6567696 51.6849876,12.3536477 53.1313394,12.4128263 C53.8325707,12.4413952 54.2674737,13.2763566 53.8149484,13.8242681 C52.3320222,15.6179895 48.3271239,16.7172136 46.2969114,17.0505179 C40.0513315,18.0766191 33.9846337,15.945512 27.8284949,15.0153209 C22.363945,14.1895424 16.1436347,13.4967455 11.1395899,16.507708 C11.0122444,16.584232 10.0024581,17.2147893 9.47545402,17.7511372 C9.0897602,18.1443003 8.43674067,18.0619945 8.17739482,17.5722412 C6.75830756,14.8939029 14.2756796,8.76008291 15.7187065,8.14993193 Z M54.9339874,11 C56.1050753,11 57.8521936,15.4015737 55.6223391,15.6527457 C53.3924847,15.9039176 53.7628995,11 54.9339874,11 Z" id="Frida" transform="translate(32.348682, 12.500000) rotate(2.000000) translate(-32.348682, -12.500000) "></path></g></g><g id="Top" stroke-width="1" fill-rule="evenodd"><defs><rect id="react-path-132" x="0" y="0" width="264" height="280"></rect><path d="M82.0179468,24.3784638 C74.9860629,23.6847351 66.6573358,23.2514721 58.3126144,23.245568 C39.0082164,23.2319099 19.618222,25.4833872 16,28 C6.29594493,35.2480719 2.53726005,62.445722 3,64 C2.67275588,65.2226722 1.34685629,65.488448 1,64 C0.280821545,53.7019177 1,1.72563718 58,1 C115,0.274362825 115.719178,53.7019177 115,64 C114.653144,65.488448 113.327244,65.2226722 113,64 C113.46274,62.445722 109.704055,35.2480719 100,28 C98.2407122,26.7763475 92.7526689,25.6101897 85.3587425,24.7390449 L89,16 L82.0179468,24.3784638 Z" id="react-path-133"></path><filter x="-0.8%" y="-2.0%" width="101.5%" height="108.0%" filterUnits="objectBoundingBox" id="react-filter-129"><feOffset dx="0" dy="2" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset><feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.16 0" type="matrix" in="shadowOffsetOuter1" result="shadowMatrixOuter1"></feColorMatrix><feMerge><feMergeNode in="shadowMatrixOuter1"></feMergeNode><feMergeNode in="SourceGraphic"></feMergeNode></feMerge></filter></defs><mask id="react-mask-130" fill="white"><use xlink:href="#react-path-132"></use></mask><g id="Mask"></g><g id="Top/Short-Hair/The-Caesar-+-Side-Part" mask="url(#react-mask-130)"><g transform="translate(-1.000000, 0.000000)"><g id="Facial-Hair/Beard-Magestic" transform="translate(49.000000, 72.000000)"><defs><path d="M65.1802189,77.7372986 C67.3631845,76.1045334 80.4065113,75.4786511 82.757829,74.0894494 C83.4916461,73.6553857 84.0610723,73.215719 84.4997781,72.7800074 C84.938814,73.215719 85.5085703,73.6553857 86.2423874,74.0894494 C88.593375,75.4786511 101.636702,76.1045334 103.819667,77.7372986 C106.030032,79.3908276 107.643571,83.1846831 107.466966,86.15095 C107.255041,89.7101408 103.361486,98.2028927 93.6723269,99.1811016 C91.5576925,96.8281927 88.2368647,95.3104528 84.4997781,95.3104528 C80.7633517,95.3104528 77.4421938,96.8281927 75.3275594,99.1811016 C65.6387308,98.2028927 61.7451757,89.7101408 61.5332501,86.15095 C61.3566455,83.1846831 62.9701849,79.3908276 65.1802189,77.7372986 M103.141638,94.9063813 C103.142958,94.9057221 103.144609,94.905063 103.145929,94.9047334 C103.144278,94.905063 103.142958,94.9057221 103.141638,94.9063813 M65.8453747,94.9014375 C65.8493359,94.9030855 65.8565982,94.9057221 65.8618798,94.9076997 C65.8565982,94.9057221 65.8509864,94.9034151 65.8453747,94.9014375 M144.86259,55.9853335 C144.47439,50.0303878 143.277769,44.1519058 142.233986,38.2862777 C141.952739,36.7072349 140.423706,26 139.734783,26 C139.502391,35.1094058 138.701893,44.0803858 137.669664,53.1393651 C137.361018,55.8475668 137.037848,58.5564277 136.825262,61.2741874 C136.653609,63.4695546 136.959614,66.1220564 136.427819,68.2455739 C135.749129,70.9524573 132.348087,73.4783984 129.702978,74.410795 C123.102915,76.7373371 117.597802,67.1077689 111.960977,64.2911336 C104.643272,60.6347152 92.0637391,59.7639895 84.5816434,64.5297918 C76.9361472,59.7639895 64.356614,60.6347152 57.0389092,64.2911336 C51.4024147,67.1077689 45.8969708,76.7373371 39.2972383,74.410795 C36.6521296,73.4783984 33.2504268,70.9524573 32.572397,68.2455739 C32.0402723,66.1220564 32.346277,63.4695546 32.174954,61.2741874 C31.9623682,58.5564277 31.6388681,55.8475668 31.3302226,53.1393651 C30.2983232,44.0803858 29.4974953,35.1094058 29.2654335,26 C28.5761802,26 27.0468169,36.7072349 26.7658999,38.2862777 C25.7221169,44.1519058 24.5258266,50.0303878 24.1376265,55.9853335 C23.738533,62.1047422 24.2148704,68.1674622 25.4695887,74.1632765 C26.0687242,77.0277016 26.7685407,79.8756475 27.518863,82.7041478 C28.352701,85.8467429 27.198994,91.9661516 27.5723395,95.1921317 C28.2787581,101.29572 31.1542781,113.199679 34.3833375,118.45096 C35.9440605,120.989096 37.7734867,122.573742 39.816489,124.619148 C41.7825775,126.58809 42.6038717,129.640049 44.7260985,131.73687 C48.6820428,135.645092 54.4456266,137.971304 60.3656788,138.543134 C65.6773527,143.050212 74.505605,146 84.4997781,146 C94.4946114,146 103.322534,143.050212 108.634538,138.543134 C114.55393,137.971304 120.317843,135.645092 124.274118,131.73687 C126.396015,129.640049 127.217309,126.58809 129.183727,124.619148 C131.2264,122.573742 133.055826,120.989096 134.616879,118.45096 C137.845608,113.199679 140.721458,101.29572 141.427547,95.1921317 C141.800892,91.9661516 140.647185,85.8467429 141.481353,82.7041478 C142.231676,79.8756475 142.931162,77.0277016 143.530628,74.1632765 C144.784686,68.1674622 145.261353,62.1047422 144.86259,55.9853335 Z" id="react-path-135"></path></defs><mask id="react-mask-134" fill="white"><use xlink:href="#react-path-135"></use></mask><use id="Glorious-Beard" fill="#5E7AA9" fill-rule="evenodd" xlink:href="#react-path-135"></use><g id="Color/Hair/Brown" mask="url(#react-mask-134)" fill="#D6B370"><g transform="translate(-32.000000, 0.000000)" id="Color"><rect x="0" y="0" width="264" height="244"></rect></g></g></g><g id="Hair" stroke-width="1" fill-rule="evenodd" transform="translate(75.000000, 34.000000)"><mask id="react-mask-131" fill="white"><use xlink:href="#react-path-133"></use></mask><use id="Caesar" fill="#28354B" xlink:href="#react-path-133"></use><g id="Skin/👶🏽-03-Brown" mask="url(#react-mask-131)" fill="#D6B370"><g transform="translate(0.000000, 0.000000) " id="Color"><rect x="0" y="0" width="264" height="280"></rect></g></g></g></g></g></g></g></g></g></g></svg>