﻿{  
    "name":"news.ycombinator",
    "request":{  
        "url":"https://news.ycombinator.com/",
        "userToken":"7f4790a8-6f76-8d6e-7c1f-d519294a8ec3",
        "type":""
    },
    "fields":[  
        {  
            "name":"selector1",
            "selector":".subtext a+ a",
            "details":{  
                "name":"selector1details",
                "request":{  
                    "url":"http://news.ycombinator.com/item?id=18932402",
                    "userToken":"",
                    "type":""
                },
                "fields":[  
                    {  
                        "name":"Story link",
                        "selector":".storylink",
                        "type":2,
                        "extractor":{  
                            "types":[  
                                "href",
                                "text"
                            ],
                            "params":{  
                                "includeIfEmpty":false
                            },
                            "filters":[  
                                "Trim"
                            ]
                        }
                    },
                    {  
                        "name":"Score",
                        "selector":".score",
                        "type":1,
                        "extractor":{  
                            "types":[  
                                "regex"
                            ],
                            "params":{  
                                "regexp":"\\d+",
                                "includeIfEmpty":false
                            },
                            "filters":[  
                                "Trim"
                            ]
                        }
                    },
                    {  
                        "name":"User",
                        "selector":".subtext .hnuser",
                        "type":2,
                        "extractor":{  
                            "types":[  
                                "href",
                                "text"
                            ],
                            "params":{  
                                "includeIfEmpty":false
                            },
                            "filters":[  
                                "Trim"
                            ]
                        }
                    },
                    {  
                        "name":"Comments",
                        "selector":".comment",
                        "type":1,
                        "extractor":{  
                            "types":[  
                                "text"
                            ],
                            "params":{  
                                "regexp":"",
                                "includeIfEmpty":false
                            },
                            "filters":[  
                                "Trim"
                            ]
                        }
                    }
                ],
                "format":"",
                "path":false,
                "preview":true
            },
            "type":2,
            "extractor":{  
                "types":[  
                    "path"
                ],
                "params":{  
                    "includeIfEmpty":false
                },
                "filters":[  
                    "Trim"
                ]
            }
        }
    ],
    "format":"csv",
    "path":true,
    "preview":true
}