﻿{
    "name":"quotes.toscrape",
    "request":{
        "url":"http://quotes.toscrape.com/"
    },
    "fields":[
        {
            "name":"selector1",
            "selector":".text",
            "extractor":{
                "types":[
                    "text"
                ],
                "params":{
                    "includeIfEmpty":false
                },
                "filters":[
                    "Trim"
                ]
            }
        },
        {
            "name":"selector2",
            "selector":".author",
            "extractor":{
                "types":[
                    "text"
                ],
                "params":{
                    "regexp":"",
                    "includeIfEmpty":false
                },
                "filters":[
                    "Trim"
                ]
            }
        },
        {
            "name":"selector3",
            "selector":".tags .tag",
            "extractor":{
                "types":[
                    "href",
                    "text"
                ],
                "params":{
                    "includeIfEmpty":false
                },
                "filters":[
                    "Trim"
                ]
            }
        }
    ],
    "format":"json"
}