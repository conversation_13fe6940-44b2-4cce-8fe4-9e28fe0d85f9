{{define "base"}}
<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <meta name="google-site-verification" content="ISx9SEOpoquwUsjBd56D_ad2n5iVaZwh7QJkYMsHtuo" />
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.1.1/css/bootstrap.min.css" integrity="sha384-WskhaSGFgHYWDcbwN70/dfYBj47jz9qbsMId/iRN3ewGhXQFZCSftd1LZCfmhktB"
        crossorigin="anonymous">
    <!-- <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-table/1.12.1/bootstrap-table.min.css"> -->
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.1.1/js/bootstrap.min.js" integrity="sha384-smHYKdLADwkXOn1EmN1qk/HfnUcbVRZyYmZ4qpPea6sjB/pTJ0euyQp0Mk8ck+5T"
        crossorigin="anonymous"></script>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.3.1/jquery.min.js"></script>

    <style>
        /* /* Sticky footer styles
-------------------------------------------------- */

        html {
            position: relative;
            min-height: 100%;
        }

        body {
            /* Margin bottom by footer height */
            margin-bottom: 60px;
        }

        .footer {
            position: absolute;
            bottom: 0;
            width: 100%;
            /* Set the fixed height of the footer here */
            min-height: 60px;
            line-height: 60px;
            /* Vertically center the text there */
            color: rgba(255, 255, 255, 1.00);
            background-color: #3c3d41;
        }


        /* Custom page CSS
-------------------------------------------------- */

        /* Not required for template or sticky footer method. */

        body>.container {
            padding: 60px 15px 0;
        }

        .footer>.container {
            padding-right: 15px;
            padding-left: 15px;
        }

        code {
            font-size: 80%;
        }

        @media (max-width: 587px) {

            .footer {
                position: static;
            }
        }
    </style>
</head>

<body>
    <header class="navbar navbar-expand navbar-dark bg-dark">
        <a class="navbar-brand" href="/">Dataflow Kit</a>
        <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarTogglerDemo02" aria-controls="navbarTogglerDemo02" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarTogglerDemo02">
             <div class="collapse navbar-collapse justify-content-between" id="navbar">
                <div class="navbar-nav">
                    <span></span>
                    {{/*<a class="nav-item nav-link" href="#">Dataflow Kit</a>*/}}
                </div>
            <div class="navbar-nav">

                {{if .User}}
                    <span class="nav-item nav-link text-warning">{{.User}}</span>
                    <a class="btn btn-primary" href="/logout">Logout</a>
                {{else}}
                    <a class="btn btn-success" href="/login">Login</a>
                {{end}}

            </div>
            </div>
        </div>
    </header>
    <div class="container mb-5">
        <h1>{{.Header}}</h1>
        <div class="alert alert-info" role="alert">
            <strong>Warning!</strong> This is a demo website for web scraping purposes. </br>The data on this page has been randomly
            generated.
            </br>
        </div>
        {{template "content" .}}
    </div>

    <footer class="footer">
        <div class="container">
            <div class="row">
                <div class="col-7">
                    <span>Avatars by:&nbsp;
                        <a href="https://getavataaars.com">getavataaars.com</a>
                    </span>
                    <span>&nbsp;&nbsp;&nbsp;Random data by:&nbsp;
                        <a href="https://www.generatedata.com">generatedata.com</a>
                    </span>
                </div>
                <div class="col-5 text-right">
                    <span class="text-right">Copyright © 2017 - 2019, Slotix s.r.o.</span>
                </div>
            </div>
        </div>
    </footer>

</body>

</html>
{{end}}