# Dataflow Kit 开发文档

## 项目概述

Dataflow Kit (DFK) 是一个用 Go 语言编写的 Web 爬虫框架，主要用于提取网页数据。它能够处理静态和动态网页（通过 Headless Chrome），并支持多种输出格式（CSV、JSON、XML）。

### 核心组件

1.  **Fetch Service (`fetch.d`)**：负责下载 HTML 网页内容。支持两种方式：
    *   **Base Fetcher**：使用标准 Go HTTP 客户端，速度快，但无法渲染 JavaScript。
    *   **Chrome Fetcher**：使用 Headless Chrome，可以处理 JavaScript 渲染的页面。
2.  **Parse Service (`parse.d`)**：负责解析已下载的 HTML 页面，并根据配置的规则提取数据，最后将数据编码成指定格式。
3.  **Storage**：用于存储中间数据（如 HTML 页面缓存、Cookies 等）。支持 Diskv 和 MongoDB。

### 数据处理流程

1.  用户向 `parse.d` 发送一个包含抓取规则的 JSON Payload。
2.  `parse.d` 将需要抓取的 URL 信息发送给 `fetch.d`。
3.  `fetch.d` 根据规则选择合适的 Fetcher 下载页面内容，并返回给 `parse.d`。
4.  `parse.d` 使用 `goquery` (类似 jQuery) 解析 HTML，并根据 Payload 中的 `fields` 定义提取数据。
5.  提取的数据会被编码成 CSV、JSON 或 XML 格式并返回给用户。

## 项目结构

```
dataflowkit/
├── cmd/                   # 可执行程序入口
│   ├── fetch.d/           # Fetch Service 主程序
│   ├── parse.d/           # Parse Service 主程序
│   └── fetch.cli/         # Fetch 命令行工具
├── fetch/                 # Fetch 服务核心逻辑 (Fetcher 实现, HTTP 服务)
├── parse/                 # Parse 服务核心逻辑 (解析逻辑, HTTP 服务)
├── scrape/                # 数据提取核心逻辑 (Payload 结构, Field 提取)
├── storage/               # 存储接口及实现 (Diskv, MongoDB)
├── errs/                  # 自定义错误类型
├── healthcheck/           # 服务健康检查
├── utils/                 # 工具函数
├── examples/              # 示例配置文件
├── ...
├── go.mod                 # Go 模块定义
└── README.md              # 项目说明
```

## 核心概念与代码解析

### 1. 服务启动 (`cmd/`)

`main.go` 是每个服务的入口点，它会调用 `Execute()` 函数（通常在 `root.go` 中定义，基于 Cobra 命令行库）来启动服务。

*   **`cmd/fetch.d/main.go`**: 启动 Fetch 服务。
*   **`cmd/parse.d/main.go`**: 启动 Parse 服务。
*   **`cmd/fetch.cli/main.go`**: 启动 Fetch 命令行工具。

### 2. HTTP 服务 (`fetch/server.go`, `parse/server.go`)

这两个文件负责启动和运行 HTTP 服务器。

*   `Start(cfg Config) *HTMLServer`: 初始化服务配置（如端口、日志）、创建核心服务实例、设置 HTTP 路由（通过 `transport.go` 中定义的 handler），然后启动 HTTP 服务器。
*   `Stop()`: 优雅地关闭服务器。

### 3. 核心服务逻辑 (`fetch/service.go`, `parse/service.go`)

这些文件定义了服务的接口和核心实现。

*   **Fetch Service (`fetch/service.go`)**:
    *   `Service` 接口定义了 `Fetch(req Request) (io.ReadCloser, error)` 方法。
    *   `FetchService` 结构体实现了 `Service` 接口。它的 `Fetch` 方法根据 `Request.Type` 选择 `BaseFetcher` 或 `ChromeFetcher` 来下载网页内容。它还处理 Cookies 的存储和读取（使用 `storage` 包）。
*   **Parse Service (`parse/service.go`)**:
    *   `Service` 接口定义了 `Parse(scrape.Payload) (io.ReadCloser, error)` 方法。
    *   `ParseService` 结构体实现了 `Service` 接口。它的 `Parse` 方法创建一个 `scrape.Task` 实例并调用其 `Parse` 方法来执行实际的数据提取。

### 4. 数据抓取与提取 (`fetch/fetcher.go`, `scrape/scrape.go`, `scrape/structure.go`)

*   **Fetcher (`fetch/fetcher.go`)**:
    *   `Fetcher` 接口定义了 `Fetch(request Request) (io.ReadCloser, error)` 方法。
    *   `BaseFetcher`: 使用 `net/http` 包下载页面。
    *   `ChromeFetcher`: 使用 `github.com/mafredri/cdp` 库与 Headless Chrome 交互，执行页面导航、等待加载、获取 DOM 等操作。
    *   `newFetcher(Type) Fetcher`: 工厂函数，根据类型创建相应的 Fetcher 实例。
*   **Payload & Field (`scrape/structure.go`)**:
    *   `Payload`: 定义了整个抓取任务的配置，包括起始 URL (`Request`)、要提取的字段 (`Fields`)、分页规则 (`Paginator`)、输出格式 (`Format`) 等。
    *   `Field`: 定义了单个数据提取规则，包括字段名 (`Name`)、CSS 选择器 (`CSSSelector`)、要提取的属性 (`Attrs`，如 `text`, `href`, `src`)、过滤器 (`Filters`，如 `trim`, `lowerCase`) 等。
*   **Scraping Logic (`scrape/scrape.go`)**:
    *   这是数据提取的核心。通常通过 `scrape.Task` 结构体来管理整个抓取过程。
    *   `Field.extract()`: 根据 `Field` 的定义，使用 `goquery` 在 HTML 片段中查找元素并提取指定属性的值。
    *   处理分页、链接跟踪（`details`）、数据编码等逻辑。

### 5. 数据存储 (`storage/storage.go`, `storage/diskv.go`, `storage/mongo.go`)

*   `Store` 接口定义了存储的基本操作：`Read`, `Write`, `IsExists`, `Expired`, `Delete`, `DeleteAll`, `Close`。
*   `NewStore(sType string) Store`: 工厂函数，根据类型（如 "diskv", "mongodb"）创建具体的存储实例。
*   `diskvConn`, `mongoConn` 等是具体存储类型的实现。

### 6. 传输层 (HTTP Handlers) (`fetch/transport.go`, `parse/transport.go`)

这些文件定义了 HTTP 路由和请求/响应的编解码。

*   定义了 HTTP 端点（如 `/fetch`, `/parse`, `/ping`）。
*   解析 HTTP 请求体（通常是 JSON 格式的 `Request` 或 `Payload`）。
*   调用对应的服务方法（`FetchService.Fetch`, `ParseService.Parse`）。
*   将服务方法返回的结果编码成 HTTP 响应（如 JSON, CSV, XML）。

### 7. 错误处理 (`errs/errors.go`)

自定义了一系列错误类型，如 `BadPayload`, `StatusError`, `ParseError` 等，方便在不同层级进行错误识别和处理。

## 如何使用 Dataflow Kit 开发应用

### 1. 理解抓取配置 (Payload)

这是使用 DFK 的核心。你需要定义一个 JSON 格式的 Payload 来描述你的抓取任务。

*   **`request`**: 指定要抓取的起始 URL 和可能需要的 HTTP 方法、表单数据等。
*   **`fields`**: 定义要提取的数据字段。每个字段包含：
    *   `name`: 字段名。
    *   `selector`: CSS 选择器，定位要提取的元素。
    *   `attrs`: 要从元素中提取的属性列表（`text`, `href`, `src`, `alt` 等）。
    *   `filters`: 对提取结果进行处理的过滤器（`trim`, `lowerCase`, `upperCase` 等）。
    *   `details`: （可选）如果该字段是链接，可以在此定义进一步抓取链接页面的规则。
*   **`paginator`**: （可选）定义分页规则，如 CSS 选择器指向“下一页”链接。
*   **`format`**: 输出格式（`json`, `csv`, `xml`）。
*   **`fetcherType`**: 指定 Fetcher 类型（`base` 或 `chrome`）。

参考 `examples/` 目录下的文件获取示例。

### 2. 部署和运行服务

#### 使用 Docker (推荐)

1.  安装 Docker 和 Docker Compose。
2.  在项目根目录运行 `docker-compose up`。这将自动拉取镜像并启动 Chrome, MongoDB, fetch.d, parse.d 服务。
3.  使用 `curl` 或其他 HTTP 客户端向 `http://localhost:8001/parse` 发送 POST 请求，请求体为你的 JSON Payload。
    ```bash
    curl -XPOST http://localhost:8001/parse --data-binary "@examples/books.toscrape.com.json"
    ```
4.  查看返回的抓取结果。
5.  停止服务：`docker-compose down --remove-orphans --volumes`。

#### 手动运行

1.  启动 Headless Chrome：
    ```bash
    docker run --init -it --rm -d --name chrome --shm-size=1024m -p=127.0.0.1:9222:9222 --cap-add=SYS_ADMIN yukinying/chrome-headless-browser
    ```
2.  构建并运行 `fetch.d`:
    ```bash
    cd cmd/fetch.d
    go build
    ./fetch.d --CHROME=http://localhost:9222 --STORAGE_TYPE=Diskv --DISKV_BASE_DIR=./diskv
    ```
3.  在新终端构建并运行 `parse.d`:
    ```bash
    cd cmd/parse.d
    go build
    ./parse.d --STORAGE_TYPE=Diskv --DISKV_BASE_DIR=./diskv --DFK_FETCH=http://localhost:8000
    ```
4.  发送 POST 请求到 `http://localhost:8001/parse`。

### 3. 开发自定义功能

*   **添加新的 Extractor/Filter**: 在 `scrape/scrape.go` 中扩展 `Field.extract` 方法或 `Filter.Apply` 方法。
*   **添加新的存储类型**: 实现 `storage/Store` 接口，并在 `storage/storage.go` 的 `NewStore` 函数中添加相应的创建逻辑。
*   **修改 Fetcher 行为**: 可以扩展现有的 `BaseFetcher` 或 `ChromeFetcher`，或者实现新的 `Fetcher`。
*   **添加新的输出格式**: 在 `scrape/encoders.go` 中实现新的编码器，并在 `parse/service.go` 中集成。

### 4. 调试和测试

*   查看服务日志输出，了解请求处理过程和潜在错误。
*   使用 `test.sh` 运行单元测试。
*   使用 `test-docker-compose.yml` 启动测试环境进行集成测试。

## 总结

Dataflow Kit 通过将抓取任务分解为 Fetch 和 Parse 两个独立的服务，实现了良好的模块化和可扩展性。理解其核心组件（Fetcher, Parser, Payload, Storage）和数据流是二次开发的关键。通过自定义 Payload 配置，你可以灵活地提取各种网页数据。